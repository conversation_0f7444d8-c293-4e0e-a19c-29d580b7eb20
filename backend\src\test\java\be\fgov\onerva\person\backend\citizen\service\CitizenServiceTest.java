package be.fgov.onerva.person.backend.citizen.service;

import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.lookup.LookupClient;
import be.fgov.onerva.person.backend.lookup.model.BelgianCommunity;
import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.PaymentType;
import be.fgov.onerva.person.backend.request.service.PersonRequestService;
import be.fgov.onerva.wave.api.UserApi;
import be.fgov.onerva.wave.model.UserCriteria;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.ErrorResponseException;
import org.springframework.web.client.HttpClientErrorException;

import java.time.LocalDate;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class CitizenServiceTest {
    @Mock
    private CitizenRepository citizenRepository;
    @Mock
    private PersonRequestService personRequestService;
    @Mock
    private LookupClient lookupClient;
    @Mock
    private UserApi userApi;
    @InjectMocks
    private CitizenService citizenService;

    @Test
    void createCitizenWithAllowanceIsUnsupported() {
        CitizenCreationRequest request = CitizenCreationRequest.builder()
                .allowance(true)
                .build();

        assertThrows(UnsupportedOperationException.class, () -> citizenService.createCitizen(request));
    }

    @Test
    void createCitizenOnlyForAdmissibilityDomain() {
        CitizenCreationRequest request = CitizenCreationRequest.builder()
                .allowance(false)
                .build();

        assertThrows(UnsupportedOperationException.class, () -> citizenService.createCitizen(request));
    }

    @Test
    void createCitizenWithInvalidNissIsForbidden() {
        CitizenCreationRequest request = CitizenCreationRequest.builder()
                .allowance(false)
                .domain(BusinessDomain.ADMISSIBILITY)
                .niss("***********")
                .build();

        assertThrows(IllegalArgumentException.class, () -> citizenService.createCitizen(request));
    }

    @Test
    void createCitizenOnlyNonExistingCitizen() {
        CitizenCreationRequest request = CitizenCreationRequest.builder()
                .allowance(false)
                .domain(BusinessDomain.ADMISSIBILITY)
                .niss("***********")
                .firstname("John")
                .lastname("Doe")
                .build();

        when(citizenRepository.existsById(any())).thenReturn(true);

        assertThrows(CitizenExistsException.class, () -> citizenService.createCitizen(request));

        var numPens = PensionNumberUtils.convertFromInss(***********L);
        verify(citizenRepository).existsById(numPens);
        verifyNoInteractions(personRequestService);
    }

    @Test
    void createCitizen() {
        CitizenCreationRequest request = CitizenCreationRequest.builder()
                .allowance(false)
                .domain(BusinessDomain.ADMISSIBILITY)
                .niss("***********")
                .firstname("John")
                .lastname("Doe")
                .correlationId("my-uuid")
                .build();

        when(citizenRepository.existsById(any())).thenReturn(false);

        citizenService.createCitizen(request);

        verify(personRequestService).createMinimalPersonInfo("John", "Doe", "***********", "my-uuid");
    }

    @Test
    void getByNumbox() {
        assertThrows(CitizenNotFoundException.class, () -> citizenService.getByNumbox(404));

        when(citizenRepository.findByNumBox(any())).thenReturn(List.of(
              CitizenEntity.builder().id(2330009).numBox(7113390).lastId(1).build(),
              CitizenEntity.builder().id(8330510).numBox(7113390).lastId(9).build()
        ));

        assertThat(citizenService.getByNumbox(7113390).getId()).isEqualTo(8330510);

        verify(citizenRepository).findByNumBox(404);
        verify(citizenRepository).findByNumBox(7113390);
    }

    @Test
    void update_citizen_invalid_inss() {
        var request = update().niss("102030001123").build();

        var ex = assertThrows(IllegalArgumentException.class, () -> citizenService.updateCitizen(request));
        assertThat(ex).hasMessage("Invalid inss: 102030001123");

        verifyNoInteractions(citizenRepository, personRequestService, lookupClient, userApi);
    }

    @Test
    void update_citizen_not_found() {
        var request = update().build();

        assertThrows(CitizenNotFoundException.class, () -> citizenService.updateCitizen(request));

        verify(citizenRepository).existsById(809010101);
        verifyNoInteractions(personRequestService, lookupClient, userApi);
    }

    @Test
    void update_citizen_invalid_nationality_code() {
        when(citizenRepository.existsById(any())).thenReturn(true);

        var request = update().nationalityCode(666).build();

        var ex = assertThrows(IllegalArgumentException.class, () -> citizenService.updateCitizen(request));
        assertThat(ex).hasMessage("Invalid nationality code: 666");

        verify(citizenRepository).existsById(809010101);
        verify(lookupClient).findAllNationalityCodes();
        verifyNoInteractions(personRequestService, userApi);
    }

    @Test
    void update_citizen_invalid_zip_code() {
        when(citizenRepository.existsById(any())).thenReturn(true);

        var request = update().build();

        var ex = assertThrows(IllegalArgumentException.class, () -> citizenService.updateCitizen(request));
        assertThat(ex).hasMessage("Invalid Belgian zip code: 1000");

        verify(citizenRepository).existsById(809010101);
        verify(lookupClient, never()).findAllNationalityCodes();
        verify(lookupClient).findBelgianZipCode(1000);
        verifyNoInteractions(personRequestService, userApi);
    }

    @Test
    void update_citizen_user_api_401() {
        when(citizenRepository.existsById(any())).thenReturn(true);
        when(lookupClient.findBelgianZipCode(anyInt())).thenReturn(List.of(BelgianCommunity.builder().build()));
        when(userApi.searchUsers(any())).thenThrow(HttpClientErrorException.create(HttpStatus.UNAUTHORIZED, "", null, null, null));

        var request = update().build();

        var ex = assertThrows(ErrorResponseException.class, () -> citizenService.updateCitizen(request));
        assertThat(ex.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(ex.getCause()).isInstanceOf(HttpClientErrorException.Unauthorized.class);

        verify(citizenRepository).existsById(809010101);
        verify(lookupClient).findBelgianZipCode(1000);
        verify(userApi).searchUsers(new UserCriteria().username("jdoe"));
        verifyNoInteractions(personRequestService);
    }

    @Test
    void update_citizen_user_api_404() {
        when(citizenRepository.existsById(any())).thenReturn(true);
        when(lookupClient.findBelgianZipCode(anyInt())).thenReturn(List.of(BelgianCommunity.builder().build()));
        when(userApi.searchUsers(any())).thenThrow(HttpClientErrorException.create(HttpStatus.NOT_FOUND, "", null, null, null));

        var request = update().build();

        var ex = assertThrows(IllegalArgumentException.class, () -> citizenService.updateCitizen(request));
        assertThat(ex.getCause()).isInstanceOf(HttpClientErrorException.NotFound.class);
        assertThat(ex.getMessage()).isEqualTo("Could not find user: jdoe");

        verify(citizenRepository).existsById(809010101);
        verify(lookupClient).findBelgianZipCode(1000);
        verify(userApi).searchUsers(new UserCriteria().username("jdoe"));
        verifyNoInteractions(personRequestService);
    }

    CitizenUpdateRequest.CitizenUpdateRequestBuilder update() {
        return CitizenUpdateRequest.builder()
                .address(Address.builder().street("Main street").number("1A").zip(1000).city("BXL").build())
                .niss("***********")
                .correlationId("my-bigint")
                .username("jdoe")
                .paymentType(PaymentType.BANK_TRANSFER)
                .valueDate(LocalDate.now());
    }
}
