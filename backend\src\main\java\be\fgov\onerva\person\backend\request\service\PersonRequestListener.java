package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.featureflags.FeatureFlagsService;
import be.fgov.onerva.person.backend.request.broker.BrokerService;
import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
import be.fgov.onerva.person.backend.request.mapper.PersonEventMapper;
import be.fgov.onerva.person.backend.request.model.PersonMfxResponse;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import jakarta.jms.JMSException;
import jakarta.jms.TextMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.mapstruct.factory.Mappers;
import org.springframework.jms.JmsException;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static be.fgov.onerva.person.backend.request.model.PersonRequest.ERROR_LENGTH;

@Slf4j
@Component
class PersonRequestListener {

    private final JmsTemplate jmsTemplate;
    private final PersonRequestRepository repository;

    private final CitizenRepository citizenRepository;
    private final BrokerService brokerService;
    private final PersonEventMapper personEventMapper;

    public PersonRequestListener(JmsTemplate jmsTemplate,
                                 PersonRequestRepository repository,
                                 CitizenRepository citizenRepository,
                                 BrokerService brokerService) {
        this.jmsTemplate = jmsTemplate;
        this.repository = repository;
        this.citizenRepository = citizenRepository;
        this.brokerService = brokerService;
        this.personEventMapper = Mappers.getMapper(PersonEventMapper.class);
    }

    @TransactionalEventListener
    @Transactional(transactionManager = "personTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void handlePersonRequestEvent(PersonRequestEvent event) {
        PersonRequest request = event.getPersonRequest();
        long id = request.getId();
        var payload = request.toRecord();
        log.debug("Handle request #{} with payload: {}", id, payload);

        try {
            jmsTemplate.convertAndSend(payload);
            repository.markAsSent(id, LocalDateTime.now());
        } catch (JmsException e) {
            log.error("Could not send request with #id " + id + " and payload : " + payload, e);
            repository.updateStatus(id, null, getErrorMessage(e), LocalDateTime.now());
        }
    }

    String getErrorMessage(Exception e) {
        String msg = ExceptionUtils.getRootCauseMessage(e);
        return msg.length() > ERROR_LENGTH ? msg.substring(0, ERROR_LENGTH) : msg;
    }

    @Transactional("personTransactionManager")
    @JmsListener(destination="${queue.out}", selector = "${queue.selector:}")
    public void handleReply(TextMessage reply) {
        try {
            String payload = reply.getText();
            log.debug("Payload: {}", payload);

            var response = PersonMfxResponse.from(payload);
            long id = response.getId();

            int rows = repository.updateStatus(id, response.getErrorCode(), response.getErrorMessage(), LocalDateTime.now());

            if (rows != 1) {
                log.error("Affected rows: {} of record: {}", rows, payload);
                throw new IllegalArgumentException("Could not update return code of PersonRequest with id: " + id);
            }
            var request = repository.findById(id).orElseThrow();
            //For defense purpose
            if (response.getType() == PersonRequestType.CREATE && response.isSuccess()) {
                var numpens = PensionNumberUtils.convertFromInss(Long.parseLong(request.getNiss()));
                waitForCitizenToBeActuallyPersisted(numpens);
            }
            //End of defense purpose
            Object message = switch (request.getType()) {
                case CREATE -> personEventMapper.mapToCloudEventCreate(request, response);
                case UPDATE -> personEventMapper.mapToCloudEventUpdate(request, response);
            };
            brokerService.convertAndSend(message);
        } catch (JMSException e) {
            log.error("Could not read message!", e);
            throw new RuntimeException(e);
        }
    }

    @Transactional("personTransactionManager")
    @Scheduled(fixedRateString = "${queue.retryInMinutes:10}", timeUnit = TimeUnit.MINUTES)
    public void retryFailedRequests() {
        repository.findFirst10BySentFalseOrderByCreatedAsc()
                .forEach(this::retry);
    }

    @Transactional(value = "personTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void retry(PersonRequest personRequest) {
        personRequest.incrementRetryCount();
        long id = personRequest.getId();
        var payload = personRequest.toRecord();

        try {
            jmsTemplate.convertAndSend(payload);
            personRequest.markAsSent();
        } catch (RuntimeException e) {
            log.error("Could not send request with #id " + id + " and payload : " + payload, e);
            personRequest.logError(getErrorMessage(e));
        }
    }

    private void waitForCitizenToBeActuallyPersisted(Integer numpens) {
        Optional<CitizenEntity> citizenEntity;
        while ((citizenEntity = citizenRepository.findById(numpens)).isEmpty()) {
            //log.debug("Citizen searched with numpens {} and not found.", numpens);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.debug("Citizen found for numpens {}: {}", numpens, citizenEntity.get());
    }

}
