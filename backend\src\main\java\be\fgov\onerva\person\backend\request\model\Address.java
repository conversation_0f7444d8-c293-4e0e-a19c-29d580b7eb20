package be.fgov.onerva.person.backend.request.model;

import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@Embeddable
@Getter
@Builder
@ToString
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
public class Address {
  @NotBlank
  private String street;

  private String number;

  private String box;
  @Min(1000) @Max(9999)
  private int zip;
  @NotBlank
  private String city;
}

