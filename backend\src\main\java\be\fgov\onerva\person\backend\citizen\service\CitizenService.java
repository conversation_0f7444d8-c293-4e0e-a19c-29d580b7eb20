package be.fgov.onerva.person.backend.citizen.service;

import be.fgov.onerva.common.utils.InssUtils;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.lookup.LookupClient;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.service.PersonRequestService;
import be.fgov.onerva.wave.api.UserApi;
import be.fgov.onerva.wave.model.User;
import be.fgov.onerva.wave.model.UserCriteria;
import io.github.perplexhub.rsql.RSQLJPASupport;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ProblemDetail;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.ErrorResponseException;
import org.springframework.web.client.HttpClientErrorException;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class CitizenService {

    private final CitizenRepository citizenRepository;
    private final PersonRequestService personRequestService;
    private final LookupClient lookupClient;
    private final UserApi userApi;

    public CitizenEntity getByNiss(@NotNull String niss) {
        var numpens = PensionNumberUtils.convertFromInss(Long.parseLong(niss));
        if(numpens == null) {
            throw new CitizenNotFoundException();
        }
        return citizenRepository.findById(numpens).orElseThrow(CitizenNotFoundException::new);
    }

    public CitizenEntity getByNumbox(@NotNull Integer numbox) {
        //TODO quick fix ... should be further analyzed
        return citizenRepository.findByNumBox(numbox).stream()
                .filter(citizen -> citizen.getLastId() == 9)
                .findAny()
                .orElseThrow(CitizenNotFoundException::new);
    }

    public Page<CitizenEntity> searchCitizenByQuery(@NotNull String query,@NotNull Pageable pageable) {
        return citizenRepository.findAll(RSQLJPASupport.rsql(query), pageable);
    }

    public PersonRequest createCitizen(@Valid @NotNull CitizenCreationRequest request) {
        if (request.isAllowance()) {
            throw new UnsupportedOperationException("Only supported for citizens that are not asking an allowance.");
        }
        if (request.getDomain() != BusinessDomain.ADMISSIBILITY) {
            throw new UnsupportedOperationException("Only business domain ADMISSIBILITY supported for the moment.");
        }
        long inss = Long.parseLong(request.getNiss());
        if (!InssUtils.isValid(inss)) {
            throw new IllegalArgumentException("Invalid inss: " + request.getNiss());
        }
        var numpens = PensionNumberUtils.convertFromInss(inss);
        if (citizenRepository.existsById(numpens)) {
            throw new CitizenExistsException();
        }
        return personRequestService.createMinimalPersonInfo(request.getFirstname(), request.getLastname(), request.getNiss(), request.getCorrelationId());
    }

    public PersonRequest updateCitizen(@Valid @NotNull CitizenUpdateRequest request) {
        long inss = Long.parseLong(request.getNiss());
        if (!InssUtils.isValid(inss)) {
            throw new IllegalArgumentException("Invalid inss: " + request.getNiss());
        }
        var numpens = PensionNumberUtils.convertFromInss(inss);
        if (!citizenRepository.existsById(numpens)) {
            throw new CitizenNotFoundException();
        }
        if (request.getNationalityCode() != null) {
            String nationality = request.getNationalityCode().toString();
            boolean nationalityExists = lookupClient.findAllNationalityCodes().stream()
                    .anyMatch(lookup -> lookup.getCode().equals(nationality));
            if (!nationalityExists) {
                throw new IllegalArgumentException("Invalid nationality code: " + nationality);
            }
        }
        if (lookupClient.findBelgianZipCode(request.getAddress().getZip()).isEmpty()) {
            throw new IllegalArgumentException("Invalid Belgian zip code: " + request.getAddress().getZip());
        }
        try {
            User user = userApi.searchUsers(new UserCriteria().username(request.getUsername()));

            return personRequestService.updatePersonInfo(request, user);
        } catch (HttpClientErrorException.Unauthorized | HttpClientErrorException.Forbidden e) {
            log.error("Not authorized to call the user endpoint.", e);
            throw new ErrorResponseException(
                    e.getStatusCode(),
                    ProblemDetail.forStatusAndDetail(e.getStatusCode(), "Not authorized to call the user endpoint! Check client security."),
                    e
            );
        } catch (HttpClientErrorException e) {
            throw new IllegalArgumentException("Could not find user: " + request.getUsername(), e);
        }
    }
}
