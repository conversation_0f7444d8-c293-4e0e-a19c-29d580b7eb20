package be.fgov.onerva.person.backend.citizen.model;

import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.PaymentType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;

@Builder
@Getter
@ToString
public class CitizenUpdateRequest {
    @NotNull
    @Pattern(regexp = "\\d{11}")
    private String niss;
    @NotNull @Valid
    private Address address;
    @Min(100) @Max(999)
    private Integer nationalityCode;
    private PaymentType paymentType;
    private Boolean unionDue;
    @NotNull
    private LocalDate valueDate;
    @NotBlank
    private String username;
    private String correlationId;
}
