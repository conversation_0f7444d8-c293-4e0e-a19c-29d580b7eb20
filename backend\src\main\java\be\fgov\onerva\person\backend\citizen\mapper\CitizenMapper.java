package be.fgov.onerva.person.backend.citizen.mapper;

import backend.rest.model.CitizenCreationRequestDTO;
import backend.rest.model.CitizenDTO;
import backend.rest.model.CitizenPageDTO;
import backend.rest.model.CitizenUpdateRequestDTO;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.data.domain.Page;

@Mapper
public interface CitizenMapper {

    @Mapping(target = "pensionNumber", source = "entity.id")
    @Mapping(target = "zipCode", source = "entity.zipCode")
    @Mapping(target = "numbox", source = "entity.numBox")
    @Mapping(target = "niss", expression = "java(niss(entity))")
    @Mapping(target = "lastname", expression = "java(lastName(entity))")
    @Mapping(target = "firstname", expression = "java(firstName(entity))")
    @Mapping(target= "agent", source= "entity.flagPersonnel")
    CitizenDTO map(CitizenEntity entity);

    @Mapping(source = "number", target = "pageNumber")
    @Mapping(source = "size", target = "pageSize")
    @Mapping(source = "totalPages", target = "totalPage")
    @Mapping(source = "totalElements", target = "totalElements")
    @Mapping(source = "first", target = "isFirst")
    @Mapping(source = "last", target = "isLast")
    CitizenPageDTO mapPageToDto(Page<CitizenEntity> source);

    @Mapping(source = "businessDomain", target = "domain")
    CitizenCreationRequest map(CitizenCreationRequestDTO source, String businessDomain, boolean allowance);

    CitizenUpdateRequest map(CitizenUpdateRequestDTO source, String niss, String username);

    default String firstName(CitizenEntity entity) {
        if (entity == null) {
            return null;
        }
        return entity.getFullName().split(",")[1].trim();
    }

    default String lastName(CitizenEntity entity) {
        if (entity == null) {
            return null;
        }
        return entity.getFullName().split(",")[0].trim();
    }

    default String niss(CitizenEntity entity) {
        if (entity == null) {
            return null;
        }

        return StringUtils.leftPad(PensionNumberUtils.convertToInssWithDefault(entity.getId()).toString(), 11, '0');
    }
}
