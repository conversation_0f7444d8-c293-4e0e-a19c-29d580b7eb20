package be.fgov.onerva.person.backend.request.model;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import static be.fgov.onerva.person.backend.request.model.PersonRequest.*;

@Builder(access = AccessLevel.PRIVATE)
@Getter
@ToString
public class PersonMfxResponse {

    static final int TYPE_LENGTH = 4;

    private PersonRequestType type;
    private long id;
    private String inss;
    private String names;
    private int errorCode;
    private boolean v1;

    public String getErrorMessage() {
        return type == PersonRequestType.CREATE ? createErrorCodes() : updateErrorCodes();
    }

    String updateErrorCodes() {
        return switch (errorCode) {
            case 0 -> null;
            case -1 -> "NISS must be numeric";
            case -2 -> "Check digit NISS not correct";
            case -3 -> "Functional code must be: 0001 or 0002";
            case -4 -> "The person must be known in MFX (keybox_ds)";
            case -300 -> "VALUE DATE is not a valid date";
            case -1003 -> "The derived pension number from NISS must be known in MFX (keybox_ds)";
            case -1005 -> "Data inconsistency in MFX : the person is known in keybox_ds but not in general_ds";
            case -1100 -> "The street name is mandatory";
            case -1101 -> "The zip code is mandatory";
            case -1102 -> "The zip code must be known in the lookup table";
            case -1103 -> "The NIS code of the new address has been found, but the unemployment office number for this address is not available in the lookup table";
            case -1104 -> "The union due must be either true or false";
            case -1105 -> "Data inconsistency in MFX: the person is not found in general_ds.";
            case -1106 -> "The payment type is not correct";
            case -1108 -> "The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds.";
            case -1110 -> "The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds.";
            case -1111 -> "The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds.";
            case -1112 -> "The nationality code must be known in the lookup table";
            case -1113 -> "The NIS code of the new address must be linked to the same unemployement office than the previous address";
            case -9105 -> "Technical error in MFX";
            case -9003 -> "Technical error in MFX";
            default -> "Unknown code: " + errorCode;
        };
    }

    String createErrorCodes() {
        return switch (errorCode) {
            case -1 -> "Invalid INSS";
            case -2 -> "Citizen already exists";
            case 1 -> null;
            default -> "Unknown code: " + errorCode;
        };
    }

    public boolean isSuccess() {
        return type == PersonRequestType.CREATE ? errorCode == -2 || errorCode == 1 : errorCode == 0;
    }

    public static PersonMfxResponse from(String response) {
        if (response.startsWith(UPDATE) || response.startsWith(CREATE)) {
            return PersonMfxResponse.builder()
                    .type(type(response))
                    .v1(false)
                    .id(id(response, false))
                    .names(names(response, false))
                    .inss(inss(response, false))
                    .errorCode(errorCode(response, false))
                    .build();
        } else {
           return PersonMfxResponse.builder()
                   .type(PersonRequestType.CREATE)
                   .v1(true)
                   .id(id(response, true))
                   .names(names(response, true))
                   .inss(inss(response, true))
                   .errorCode(errorCode(response, true))
                   .build();
        }
    }

    static PersonRequestType type(String response) {
        String typeCode = response.substring(0, TYPE_LENGTH);
        return switch (typeCode) {
            case CREATE -> PersonRequestType.CREATE;
            case UPDATE -> PersonRequestType.UPDATE;
            default -> throw new IllegalArgumentException("Unknown type: " + typeCode);
        };
    }

    static String names(String response, boolean v1) {
        int start = v1 ? 0 : TYPE_LENGTH + ID_LENGTH;
        int end = start + NAMES_LENGTH;
        return response.substring(start, end).trim();
    }

    static String inss(String response, boolean v1) {
        int start = v1 ? NAMES_LENGTH : TYPE_LENGTH + ID_LENGTH + NAMES_LENGTH;
        int end = start + INSS_LENGTH;
        return response.substring(start, end);
    }

    static long id(String response, boolean v1) {
        int start = v1 ? NAMES_LENGTH + INSS_LENGTH : TYPE_LENGTH;
        int end = start + ID_LENGTH;
        return Long.parseLong(response.substring(start, end));
    }

    static int errorCode(String response, boolean v1) {
        int start = v1 ? NAMES_LENGTH + INSS_LENGTH + ID_LENGTH : TYPE_LENGTH + ID_LENGTH + NAMES_LENGTH + INSS_LENGTH;
        int end = start + CODE_LENGTH;
        return Integer.parseInt(response.substring(start, end));
    }

}
